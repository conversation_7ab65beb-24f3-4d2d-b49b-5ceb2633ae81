import { MapPin, Phone, Mail, Smartphone } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-16">
        {/* Main footer content */}
        <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8 mb-12">
          {/* Brand section */}
          <div className="lg:col-span-1">
            <h3 className="text-2xl font-bold text-primary mb-4">King<PERSON></h3>
            <p className="text-gray-300 mb-6 leading-relaxed">
              La plateforme de livraison à la demande qui révolutionne les services à Madagascar.
              Rapide, sécurisé, géolocalisé.
            </p>
            <div className="flex space-x-4">
              <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                <Smartphone className="w-5 h-5 text-primary" />
              </div>
              <div>
                <p className="font-semibold">Téléchargez l'app</p>
                <p className="text-sm text-gray-400">Disponible bientôt</p>
              </div>
            </div>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Services</h4>
            <ul className="space-y-3">
              <li><a href="#" className="text-gray-300 hover:text-primary transition-colors">Livraison Express</a></li>
              <li><a href="#" className="text-gray-300 hover:text-primary transition-colors">Envoi de Colis</a></li>
              <li><a href="#" className="text-gray-300 hover:text-primary transition-colors">Shopping à la demande</a></li>
              <li><a href="#" className="text-gray-300 hover:text-primary transition-colors">Services professionnels</a></li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Support</h4>
            <ul className="space-y-3">
              <li><a href="#" className="text-gray-300 hover:text-primary transition-colors">Centre d'aide</a></li>
              <li><a href="#" className="text-gray-300 hover:text-primary transition-colors">Comment ça marche</a></li>
              <li><a href="#" className="text-gray-300 hover:text-primary transition-colors">Devenir partenaire</a></li>
              <li><a href="#" className="text-gray-300 hover:text-primary transition-colors">Conditions d'utilisation</a></li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Contact</h4>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <MapPin className="w-5 h-5 text-primary" />
                <span className="text-gray-300">Antananarivo, Madagascar</span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-primary" />
                <span className="text-gray-300">+261 XX XX XXX XX</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-primary" />
                <span className="text-gray-300"><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="border-t border-gray-800 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-gray-400 text-sm">
              © 2024 Kinga. Tous droits réservés.
            </p>
            <div className="flex space-x-6">
              <a href="#" className="text-gray-400 hover:text-primary text-sm transition-colors">
                Politique de confidentialité
              </a>
              <a href="#" className="text-gray-400 hover:text-primary text-sm transition-colors">
                Mentions légales
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;