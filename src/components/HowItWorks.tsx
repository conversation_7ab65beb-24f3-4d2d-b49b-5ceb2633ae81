import { Card, CardContent } from "@/components/ui/card";
import { UserPlus, MapPin, Truck, CheckCircle } from "lucide-react";

const HowItWorks = () => {
  const steps = [
    {
      icon: <UserPlus className="w-8 h-8 text-white" />,
      title: "Créer un compte",
      description: "Inscrivez-vous rapidement avec vos informations de base et activez la géolocalisation.",
      details: ["Formulaire simple", "Vérification rapide", "Autorisation GPS"]
    },
    {
      icon: <MapPin className="w-8 h-8 text-white" />,
      title: "Choisir les adresses",
      description: "Entrez l'adresse de collecte et de livraison avec toutes les instructions nécessaires.",
      details: ["Adresse de collecte", "Adresse de livraison", "Instructions spéciales"]
    },
    {
      icon: <Truck className="w-8 h-8 text-white" />,
      title: "Sélectionner le service",
      description: "Choisissez le type de véhicule adapté et votre mode de paiement préféré.",
      details: ["Type de véhicule", "Mode de paiement", "Estimation tarifaire"]
    },
    {
      icon: <CheckCircle className="w-8 h-8 text-white" />,
      title: "Suivi en temps réel",
      description: "Recevez vos codes de sécurité et suivez votre commande en temps réel.",
      details: ["Code de collecte", "Code de livraison", "Suivi GPS"]
    }
  ];

  return (
    <section id="comment-ca-marche" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6 text-gray-900">
            Comment ça <span className="text-primary">fonctionne</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Un processus simple en 4 étapes pour bénéficier de nos services de livraison géolocalisée.
          </p>
        </div>

        {/* Steps */}
        <div className="relative">
          {/* Connection line for desktop */}
          <div className="hidden lg:block absolute top-24 left-1/2 transform -translate-x-1/2 w-3/4 h-0.5 bg-gradient-to-r from-primary via-primary to-primary opacity-20" />
          
          <div className="grid lg:grid-cols-4 gap-8">
            {steps.map((step, index) => (
              <div key={index} className="relative">
                {/* Step number and icon */}
                <div className="flex flex-col items-center mb-6">
                  <div className="relative">
                    <div className="w-20 h-20 bg-gradient-to-br from-primary to-primary-glow rounded-full flex items-center justify-center shadow-lg mb-4">
                      {step.icon}
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md border-2 border-primary">
                      <span className="text-primary font-bold text-sm">{index + 1}</span>
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-center text-gray-900 mb-3">
                    {step.title}
                  </h3>
                </div>

                {/* Step card */}
                <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <CardContent className="p-6">
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {step.description}
                    </p>
                    
                    <ul className="space-y-2">
                      {step.details.map((detail, detailIndex) => (
                        <li key={detailIndex} className="flex items-center text-sm text-gray-700">
                          <div className="w-1.5 h-1.5 bg-primary rounded-full mr-3" />
                          {detail}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>

        {/* Security notice */}
        <div className="mt-16 text-center">
          <div className="inline-flex items-center space-x-3 bg-primary/10 px-6 py-3 rounded-full">
            <CheckCircle className="w-5 h-5 text-primary" />
            <span className="text-primary font-semibold">
              Système de codes de sécurité unique pour chaque livraison
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;