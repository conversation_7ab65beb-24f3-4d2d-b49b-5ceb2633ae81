import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>u, X } from "lucide-react";
import { useState } from "react";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-100">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <h1 className="text-2xl font-bold text-primary">Kinga</h1>
          </div>

          {/* Desktop navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <a href="#accueil" className="text-gray-700 hover:text-primary transition-colors font-medium">
              Accueil
            </a>
            <a href="#services" className="text-gray-700 hover:text-primary transition-colors font-medium">
              Services
            </a>
            <a href="#comment-ca-marche" className="text-gray-700 hover:text-primary transition-colors font-medium">
              Comment ça marche
            </a>
            <a href="#contact" className="text-gray-700 hover:text-primary transition-colors font-medium">
              Contact
            </a>
          </div>

          {/* CTA Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <Button variant="outline" className="border-primary text-primary hover:bg-primary/10">
              Se connecter
            </Button>
            <Button className="bg-primary hover:bg-primary/90 text-white">
              S'inscrire
            </Button>
          </div>

          {/* Mobile menu button */}
          <button
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? (
              <X className="w-6 h-6 text-gray-700" />
            ) : (
              <Menu className="w-6 h-6 text-gray-700" />
            )}
          </button>
        </div>

        {/* Mobile navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-100">
            <div className="flex flex-col space-y-4">
              <a href="#accueil" className="text-gray-700 hover:text-primary transition-colors font-medium px-2 py-1">
                Accueil
              </a>
              <a href="#services" className="text-gray-700 hover:text-primary transition-colors font-medium px-2 py-1">
                Services
              </a>
              <a href="#comment-ca-marche" className="text-gray-700 hover:text-primary transition-colors font-medium px-2 py-1">
                Comment ça marche
              </a>
              <a href="#contact" className="text-gray-700 hover:text-primary transition-colors font-medium px-2 py-1">
                Contact
              </a>
              <div className="flex flex-col space-y-2 pt-4 border-t border-gray-100">
                <Button variant="outline" className="border-primary text-primary hover:bg-primary/10">
                  Se connecter
                </Button>
                <Button className="bg-primary hover:bg-primary/90 text-white">
                  S'inscrire
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;