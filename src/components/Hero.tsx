import { But<PERSON> } from "@/components/ui/button";
import { Map<PERSON>in, Clock, Shield, Smartphone } from "lucide-react";

const Hero = () => {
  return (
    <section className="relative min-h-screen bg-gradient-to-br from-primary to-primary-glow overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-10 w-32 h-32 rounded-full bg-white/10" />
        <div className="absolute bottom-32 left-10 w-24 h-24 rounded-full bg-white/5" />
        <div className="absolute top-1/2 right-1/3 w-16 h-16 rounded-full bg-white/10" />
      </div>

      <div className="relative container mx-auto px-4 pt-20 pb-16">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          {/* Left content */}
          <div className="text-white">
            <h1 className="text-5xl lg:text-7xl font-bold mb-6 leading-tight">
              King<PERSON> réinvente
              <br />
              <span className="text-white/90">les services</span>
            </h1>
            
            <p className="text-xl lg:text-2xl mb-8 text-white/90 leading-relaxed">
              Des services à la demande accessibles immédiatement et en temps-réel 
              à partir d'une application unique à Madagascar.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 mb-12">
              <Button 
                size="lg" 
                className="bg-white text-primary hover:bg-white/90 font-semibold px-8 py-4 text-lg"
              >
                Commencer maintenant
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                className="border-white/30 text-white hover:bg-white/10 font-semibold px-8 py-4 text-lg"
              >
                Comment ça marche
              </Button>
            </div>

            {/* Features highlight */}
            <div className="grid grid-cols-2 gap-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                  <MapPin className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold">Géolocalisation</h3>
                  <p className="text-white/70 text-sm">Suivi en temps réel</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                  <Clock className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold">Livraison rapide</h3>
                  <p className="text-white/70 text-sm">Service immédiat</p>
                </div>
              </div>
            </div>
          </div>

          {/* Right content - App preview */}
          <div className="relative">
            <div className="relative mx-auto w-80 h-[600px] bg-gradient-to-b from-white/20 to-white/10 rounded-[3rem] p-6 backdrop-blur-sm border border-white/20">
              {/* Phone screen content */}
              <div className="w-full h-full bg-white rounded-[2.5rem] p-6 shadow-2xl">
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold text-primary mb-2">Kinga</h2>
                  <p className="text-gray-600">Votre assistant de livraison</p>
                </div>

                {/* Service icons */}
                <div className="grid grid-cols-3 gap-4 mb-8">
                  {[
                    { icon: "🚚", label: "Livraison" },
                    { icon: "🛒", label: "Shopping" },
                    { icon: "🏪", label: "Commerces" },
                    { icon: "🍽️", label: "Restaurant" },
                    { icon: "⚡", label: "Express" },
                    { icon: "📦", label: "Colis" },
                  ].map((service, index) => (
                    <div key={index} className="text-center p-3 rounded-lg bg-gray-50">
                      <div className="text-2xl mb-2">{service.icon}</div>
                      <div className="text-xs font-medium text-gray-700">{service.label}</div>
                    </div>
                  ))}
                </div>

                {/* Map placeholder */}
                <div className="bg-gray-100 rounded-lg h-32 mb-4 flex items-center justify-center">
                  <MapPin className="w-8 h-8 text-primary" />
                </div>

                {/* Action button */}
                <Button className="w-full bg-primary hover:bg-primary/90 text-white font-semibold">
                  Nouvelle commande
                </Button>
              </div>
            </div>

            {/* Floating elements */}
            <div className="absolute -top-4 -left-4 w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <div className="absolute -bottom-4 -right-4 w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <Smartphone className="w-8 h-8 text-white" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;