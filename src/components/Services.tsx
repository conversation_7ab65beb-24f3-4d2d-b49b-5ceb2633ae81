import { Card, CardContent } from "@/components/ui/card";
import { Package, Truck, ShoppingBag, MapPin, Clock, Shield } from "lucide-react";

const Services = () => {
  const services = [
    {
      icon: <Truck className="w-8 h-8 text-primary" />,
      title: "Livraison Express",
      description: "Livraison rapide et sécurisée partout à Madagascar avec suivi en temps réel.",
      features: ["Suivi GPS", "Livraison 24h/7j", "Support client"]
    },
    {
      icon: <Package className="w-8 h-8 text-primary" />,
      title: "Envoi de Colis",
      description: "Envoyez vos colis facilement avec notre réseau de livreurs professionnels.",
      features: ["Tarifs compétitifs", "Assurance incluse", "Codes de sécurité"]
    },
    {
      icon: <ShoppingBag className="w-8 h-8 text-primary" />,
      title: "Shopping à la demande",
      description: "Commandez dans vos magasins préférés et recevez vos achats rapidement.",
      features: ["Large réseau", "Paiement sécurisé", "Choix flexible"]
    }
  ];

  const features = [
    {
      icon: <MapPin className="w-6 h-6 text-primary" />,
      title: "Géolocalisation précise",
      description: "Suivi en temps réel de vos commandes"
    },
    {
      icon: <Clock className="w-6 h-6 text-primary" />,
      title: "Livraison rapide",
      description: "Service disponible 24h/24 et 7j/7"
    },
    {
      icon: <Shield className="w-6 h-6 text-primary" />,
      title: "Sécurisé",
      description: "Codes de collecte et livraison uniques"
    }
  ];

  return (
    <section id="services" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6 text-gray-900">
            UNE APP <span className="text-primary">POUR TOUS LES SERVICES</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Commander facilement des services ou des produits via l'application Kinga. 
            Une plate-forme digitale innovante qui réinvente la relation entre professionnels et clients.
          </p>
        </div>

        {/* Main services */}
        <div className="grid md:grid-cols-3 gap-8 mb-20">
          {services.map((service, index) => (
            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 bg-white">
              <CardContent className="p-8">
                <div className="mb-6">
                  <div className="w-16 h-16 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                    {service.icon}
                  </div>
                  <h3 className="text-2xl font-bold mb-3 text-gray-900">{service.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{service.description}</p>
                </div>
                
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-gray-700">
                      <div className="w-2 h-2 bg-primary rounded-full mr-3" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Features grid */}
        <div className="grid md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center">
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                {feature.icon}
              </div>
              <h4 className="text-lg font-semibold mb-2 text-gray-900">{feature.title}</h4>
              <p className="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;