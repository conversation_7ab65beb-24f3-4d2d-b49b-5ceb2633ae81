/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #DC2626;
  --primary-glow: #EF4444;
  --secondary-color: #F3F4F6;
  --text-dark: #1F2937;
  --text-light: #6B7280;
  --white: #FFFFFF;
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-900: #111827;
  --shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.btn-primary {
  background: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background: var(--primary-glow);
  transform: translateY(-2px);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: var(--white);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.navbar .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  transition: transform 0.3s ease;
}

.logo-container:hover {
  transform: scale(1.05);
}

.logo-k {
  font-size: 1.8rem;
  font-weight: 900;
  color: var(--primary-color);
  position: relative;
  display: inline-block;
}

.logo-k::after {
  content: '🛒';
  position: absolute;
  top: -0.2rem;
  right: -0.3rem;
  font-size: 0.8rem;
  opacity: 0.7;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--primary-color);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: var(--text-dark);
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: var(--primary-color);
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.nav-toggle span {
  width: 25px;
  height: 3px;
  background: var(--text-dark);
  transition: 0.3s;
}

/* Auth Dropdown */
.auth-dropdown {
  position: relative;
}

.auth-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.auth-btn .fa-chevron-down {
  font-size: 0.75rem;
  transition: transform 0.3s ease;
}

.auth-dropdown.active .auth-btn .fa-chevron-down {
  transform: rotate(180deg);
}

.auth-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--white);
  border-radius: 0.75rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(0, 0, 0, 0.1);
  min-width: 280px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
  margin-top: 0.5rem;
}

.auth-dropdown.active .auth-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.auth-section {
  padding: 1rem;
  border-bottom: 1px solid var(--gray-100);
}

.auth-section:last-child {
  border-bottom: none;
}

.auth-section h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.auth-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  text-decoration: none;
  color: var(--text-light);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
  margin-bottom: 0.25rem;
}

.auth-link:hover {
  background: var(--gray-50);
  color: var(--primary-color);
  transform: translateX(4px);
}

.auth-link:last-child {
  margin-bottom: 0;
}

.auth-link i {
  width: 16px;
  text-align: center;
  font-size: 0.875rem;
}

/* Hero Section */
.hero {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-glow) 100%);
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-bg {
  position: absolute;
  inset: 0;
}

.hero-decoration {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.hero-decoration-1 {
  top: 5rem;
  right: 2.5rem;
  width: 8rem;
  height: 8rem;
}

.hero-decoration-2 {
  bottom: 8rem;
  left: 2.5rem;
  width: 6rem;
  height: 6rem;
  background: rgba(255, 255, 255, 0.05);
}

.hero-decoration-3 {
  top: 50%;
  right: 33%;
  width: 4rem;
  height: 4rem;
}

.hero-content {
  position: relative;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  min-height: 80vh;
  padding-top: 5rem;
  padding-bottom: 4rem;
}

.hero-text {
  color: var(--white);
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  opacity: 0.9;
}

.hero-description {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
}

.hero-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.feature-icon {
  width: 3rem;
  height: 3rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.feature-text h3 {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.feature-text p {
  opacity: 0.7;
  font-size: 0.875rem;
}

/* Phone Mockup */
.hero-phone {
  position: relative;
  display: flex;
  justify-content: center;
}

.phone-mockup {
  position: relative;
  width: 20rem;
  height: 37.5rem;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border-radius: 3rem;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: var(--white);
  border-radius: 2.5rem;
  padding: 1.5rem;
  box-shadow: var(--shadow-lg);
}

.phone-header {
  text-align: center;
  margin-bottom: 2rem;
}

.phone-header h2 {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.phone-header p {
  color: var(--text-light);
}

.phone-services {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.service-icon {
  text-align: center;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background: var(--gray-50);
  font-size: 1.5rem;
}

.service-icon span {
  display: block;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-dark);
  margin-top: 0.5rem;
}

.phone-map {
  background: var(--gray-100);
  border-radius: 0.5rem;
  height: 8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 2rem;
  color: var(--primary-color);
}

.phone-btn {
  width: 100%;
  background: var(--primary-color);
  color: var(--white);
  border: none;
  padding: 0.75rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
}

.phone-floating {
  position: absolute;
  width: 4rem;
  height: 4rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  font-size: 2rem;
  color: var(--white);
}

.phone-floating-1 {
  top: -1rem;
  left: -1rem;
}

.phone-floating-2 {
  bottom: -1rem;
  right: -1rem;
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  color: var(--text-dark);
}

.section-header p {
  font-size: 1.25rem;
  color: var(--text-light);
  max-width: 48rem;
  margin: 0 auto;
}

.text-primary {
  color: var(--primary-color);
}

/* Services Section */
.services {
  padding: 5rem 0;
  background: var(--gray-50);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.service-card {
  background: var(--white);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  text-align: center;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.service-card .service-icon {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-glow));
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 1.5rem;
  color: var(--white);
}

.service-card h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-dark);
}

.service-card p {
  color: var(--text-light);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.service-features {
  list-style: none;
  text-align: left;
}

.service-features li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-light);
  font-size: 0.875rem;
}

.service-features li::before {
  content: "•";
  color: var(--primary-color);
  font-weight: bold;
}

.features-highlight {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.feature-highlight {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: var(--white);
  padding: 1.5rem;
  border-radius: 1rem;
  box-shadow: var(--shadow);
}

.feature-highlight-icon {
  width: 3rem;
  height: 3rem;
  background: var(--primary-color);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1.25rem;
}

.feature-highlight-text h3 {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--text-dark);
}

.feature-highlight-text p {
  color: var(--text-light);
  font-size: 0.875rem;
}

/* How It Works Section */
.how-it-works {
  padding: 5rem 0;
  background: var(--white);
}

.steps-container {
  position: relative;
}

.steps-line {
  display: none;
  position: absolute;
  top: 6rem;
  left: 50%;
  transform: translateX(-50%);
  width: 75%;
  height: 2px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-glow));
  opacity: 0.2;
}

.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.step-item {
  text-align: center;
}

.step-icon-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1.5rem;
}

.step-icon {
  width: 5rem;
  height: 5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-glow));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 2rem;
  box-shadow: var(--shadow-lg);
  margin-bottom: 1rem;
}

.step-number {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  width: 2rem;
  height: 2rem;
  background: var(--white);
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: var(--primary-color);
  font-size: 0.875rem;
  box-shadow: var(--shadow);
}

.step-item h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1.5rem;
}

.step-card {
  background: var(--white);
  padding: 1.5rem;
  border-radius: 1rem;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  text-align: left;
}

.step-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.step-card p {
  color: var(--text-light);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.step-card ul {
  list-style: none;
}

.step-card li {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  color: var(--text-dark);
  font-size: 0.875rem;
}

.step-card li::before {
  content: "";
  width: 0.375rem;
  height: 0.375rem;
  background: var(--primary-color);
  border-radius: 50%;
}

/* Footer */
.footer {
  background: var(--gray-900);
  color: var(--white);
  padding: 4rem 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.footer-logo-container {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  margin-bottom: 1rem;
}

.footer-logo-k {
  font-size: 2rem;
  font-weight: 900;
  color: var(--primary-color);
  position: relative;
  display: inline-block;
}

.footer-logo-k::after {
  content: '🛒';
  position: absolute;
  top: -0.2rem;
  right: -0.4rem;
  font-size: 1rem;
  opacity: 0.7;
}

.footer-logo-text {
  font-size: 1.8rem;
  font-weight: 800;
  color: var(--primary-color);
}

.footer-brand p {
  color: #D1D5DB;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.footer-app {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.footer-app-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(220, 38, 38, 0.2);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.footer-links h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.footer-links ul {
  list-style: none;
}

.footer-links li {
  margin-bottom: 0.75rem;
}

.footer-links a {
  color: #D1D5DB;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--primary-color);
}

.footer-contact h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  color: #D1D5DB;
}

.contact-item i {
  color: var(--primary-color);
  font-size: 1.25rem;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid #374151;
  color: #9CA3AF;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.footer-social a {
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(220, 38, 38, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.3s ease;
}

.footer-social a:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .steps-line {
    display: block;
  }

  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .hero-title {
    font-size: 3rem;
  }

  .phone-mockup {
    width: 16rem;
    height: 30rem;
  }
}

@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 100%;
    left: 0;
    width: 100%;
    background: var(--white);
    flex-direction: column;
    padding: 2rem;
    box-shadow: var(--shadow);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .nav-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-toggle {
    display: flex;
  }

  .auth-dropdown {
    width: 100%;
    margin-top: 1rem;
  }

  .auth-btn {
    width: 100%;
    justify-content: center;
  }

  .auth-menu {
    position: static;
    transform: none;
    margin-top: 0.5rem;
    width: 100%;
    box-shadow: none;
    border: 1px solid var(--gray-100);
  }

  .auth-dropdown.active .auth-menu {
    transform: none;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1.125rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .hero-features {
    grid-template-columns: 1fr;
  }

  .section-header h2 {
    font-size: 2.5rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .steps-grid {
    grid-template-columns: 1fr;
  }

  .steps-line {
    display: none;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.75rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .phone-mockup {
    width: 14rem;
    height: 26rem;
    padding: 1rem;
  }

  .phone-services {
    grid-template-columns: repeat(2, 1fr);
  }
}
