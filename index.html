<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Kinga - Livraison à la demande Madagascar</title>
    <meta name="description" content="Kinga réinvente les services de livraison à Madagascar. Des services à la demande accessibles immédiatement et en temps-réel." />
    <meta name="author" content="Kinga Madagascar" />

    <meta property="og:title" content="Kinga - Livraison à la demande Madagascar" />
    <meta property="og:description" content="Kinga réinvente les services de livraison à Madagascar. Des services à la demande accessibles immédiatement et en temps-réel." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/kinga-og-image.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Kinga - Livraison à la demande Madagascar" />
    <meta name="twitter:description" content="Kinga réinvente les services de livraison à Madagascar. Des services à la demande accessibles immédiatement et en temps-réel." />

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
      /* Reset and Base Styles */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      :root {
        --primary-color: #DC2626;
        --primary-glow: #EF4444;
        --secondary-color: #F3F4F6;
        --text-dark: #1F2937;
        --text-light: #6B7280;
        --white: #FFFFFF;
        --gray-50: #F9FAFB;
        --gray-100: #F3F4F6;
        --gray-900: #111827;
        --shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      }

      body {
        font-family: 'Inter', sans-serif;
        line-height: 1.6;
        color: var(--text-dark);
        overflow-x: hidden;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
      }

      /* Buttons */
      .btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.875rem;
      }

      .btn-primary {
        background: var(--primary-color);
        color: var(--white);
      }

      .btn-primary:hover {
        background: var(--primary-glow);
        transform: translateY(-2px);
      }

      .btn-secondary {
        background: rgba(255, 255, 255, 0.2);
        color: var(--white);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .btn-secondary:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
      }

      /* Navigation */
      .navbar {
        position: fixed;
        top: 0;
        width: 100%;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        z-index: 1000;
        padding: 1rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      }

      .navbar .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo-container {
        display: flex;
        align-items: center;
        gap: 0.2rem;
        transition: transform 0.3s ease;
      }

      .logo-container:hover {
        transform: scale(1.05);
      }

      .logo-k {
        font-size: 1.8rem;
        font-weight: 900;
        color: var(--primary-color);
        position: relative;
        display: inline-block;
      }

      .logo-k::after {
        content: '🛒';
        position: absolute;
        top: -0.2rem;
        right: -0.3rem;
        font-size: 0.8rem;
        opacity: 0.7;
      }

      .logo-text {
        font-size: 1.5rem;
        font-weight: 800;
        color: var(--primary-color);
      }

      .nav-menu {
        display: flex;
        align-items: center;
        gap: 2rem;
      }

      .nav-link {
        text-decoration: none;
        color: var(--text-dark);
        font-weight: 500;
        transition: color 0.3s ease;
      }

      .nav-link:hover {
        color: var(--primary-color);
      }

      .nav-toggle {
        display: none;
        flex-direction: column;
        cursor: pointer;
        gap: 4px;
      }

      .nav-toggle span {
        width: 25px;
        height: 3px;
        background: var(--text-dark);
        transition: 0.3s;
      }

      /* Auth Dropdown */
      .auth-dropdown {
        position: relative;
      }

      .auth-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        position: relative;
      }

      .auth-btn .fa-chevron-down {
        font-size: 0.75rem;
        transition: transform 0.3s ease;
      }

      .auth-dropdown.active .auth-btn .fa-chevron-down {
        transform: rotate(180deg);
      }

      .auth-menu {
        position: absolute;
        top: 100%;
        right: 0;
        background: var(--white);
        border-radius: 0.75rem;
        box-shadow: var(--shadow-lg);
        border: 1px solid rgba(0, 0, 0, 0.1);
        min-width: 280px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s ease;
        z-index: 1000;
        margin-top: 0.5rem;
      }

      .auth-dropdown.active .auth-menu {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }

      .auth-section {
        padding: 1rem;
        border-bottom: 1px solid var(--gray-100);
      }

      .auth-section:last-child {
        border-bottom: none;
      }

      .auth-section h4 {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-dark);
        margin-bottom: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .auth-link {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.5rem 0.75rem;
        border-radius: 0.5rem;
        text-decoration: none;
        color: var(--text-light);
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s ease;
        margin-bottom: 0.25rem;
      }

      .auth-link:hover {
        background: var(--gray-50);
        color: var(--primary-color);
        transform: translateX(4px);
      }

      .auth-link:last-child {
        margin-bottom: 0;
      }

      .auth-link i {
        width: 16px;
        text-align: center;
        font-size: 0.875rem;
      }

      /* Hero Section */
      .hero {
        position: relative;
        min-height: 100vh;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-glow) 100%);
        display: flex;
        align-items: center;
        overflow: hidden;
      }

      .hero-bg {
        position: absolute;
        inset: 0;
      }

      .hero-decoration {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
      }

      .hero-decoration-1 {
        top: 5rem;
        right: 2.5rem;
        width: 8rem;
        height: 8rem;
      }

      .hero-decoration-2 {
        bottom: 8rem;
        left: 2.5rem;
        width: 6rem;
        height: 6rem;
        background: rgba(255, 255, 255, 0.05);
      }

      .hero-decoration-3 {
        top: 50%;
        right: 33%;
        width: 4rem;
        height: 4rem;
      }

      .hero-content {
        position: relative;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
        align-items: center;
        min-height: 80vh;
        padding-top: 5rem;
        padding-bottom: 4rem;
      }

      .hero-text {
        color: var(--white);
      }

      .hero-title {
        font-size: 3.5rem;
        font-weight: 800;
        line-height: 1.1;
        margin-bottom: 1.5rem;
      }

      .hero-subtitle {
        opacity: 0.9;
      }

      .hero-description {
        font-size: 1.25rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        line-height: 1.6;
      }

      .hero-buttons {
        display: flex;
        gap: 1rem;
        margin-bottom: 3rem;
      }

      .hero-features {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
      }

      .feature-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
      }

      .feature-icon {
        width: 3rem;
        height: 3rem;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
      }

      .feature-text h3 {
        font-weight: 600;
        margin-bottom: 0.25rem;
      }

      .feature-text p {
        opacity: 0.7;
        font-size: 0.875rem;
      }

      /* Phone Mockup */
      .hero-phone {
        position: relative;
        display: flex;
        justify-content: center;
      }

      .phone-mockup {
        position: relative;
        width: 20rem;
        height: 37.5rem;
        background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
        border-radius: 3rem;
        padding: 1.5rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .phone-screen {
        width: 100%;
        height: 100%;
        background: var(--white);
        border-radius: 2.5rem;
        padding: 1.5rem;
        box-shadow: var(--shadow-lg);
      }

      .phone-header {
        text-align: center;
        margin-bottom: 2rem;
      }

      .phone-header h2 {
        font-size: 1.5rem;
        font-weight: 800;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
      }

      .phone-header p {
        color: var(--text-light);
      }

      .phone-services {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
        margin-bottom: 2rem;
      }

      .service-icon {
        text-align: center;
        padding: 0.75rem;
        border-radius: 0.5rem;
        background: var(--gray-50);
        font-size: 1.5rem;
      }

      .service-icon span {
        display: block;
        font-size: 0.75rem;
        font-weight: 500;
        color: var(--text-dark);
        margin-top: 0.5rem;
      }

      .phone-map {
        background: var(--gray-100);
        border-radius: 0.5rem;
        height: 8rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        font-size: 2rem;
        color: var(--primary-color);
      }

      .phone-btn {
        width: 100%;
        background: var(--primary-color);
        color: var(--white);
        border: none;
        padding: 0.75rem;
        border-radius: 0.5rem;
        font-weight: 600;
        cursor: pointer;
      }

      .phone-floating {
        position: absolute;
        width: 4rem;
        height: 4rem;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
        font-size: 2rem;
        color: var(--white);
      }

      .phone-floating-1 {
        top: -1rem;
        left: -1rem;
      }

      .phone-floating-2 {
        bottom: -1rem;
        right: -1rem;
      }

      /* Section Headers */
      .section-header {
        text-align: center;
        margin-bottom: 4rem;
      }

      .section-header h2 {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 1.5rem;
        color: var(--text-dark);
      }

      .section-header p {
        font-size: 1.25rem;
        color: var(--text-light);
        max-width: 48rem;
        margin: 0 auto;
      }

      .text-primary {
        color: var(--primary-color);
      }

      /* Services Section */
      .services {
        padding: 5rem 0;
        background: var(--gray-50);
      }

      .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 4rem;
      }

      .service-card {
        background: var(--white);
        padding: 2rem;
        border-radius: 1rem;
        box-shadow: var(--shadow);
        transition: all 0.3s ease;
        text-align: center;
      }

      .service-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
      }

      .service-card .service-icon {
        width: 4rem;
        height: 4rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-glow));
        border-radius: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 1.5rem;
        color: var(--white);
      }

      .service-card h3 {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: var(--text-dark);
      }

      .service-card p {
        color: var(--text-light);
        margin-bottom: 1.5rem;
        line-height: 1.6;
      }

      .service-features {
        list-style: none;
        text-align: left;
      }

      .service-features li {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
        color: var(--text-light);
        font-size: 0.875rem;
      }

      .service-features li::before {
        content: "•";
        color: var(--primary-color);
        font-weight: bold;
      }

      .features-highlight {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
      }

      .feature-highlight {
        display: flex;
        align-items: center;
        gap: 1rem;
        background: var(--white);
        padding: 1.5rem;
        border-radius: 1rem;
        box-shadow: var(--shadow);
      }

      .feature-highlight-icon {
        width: 3rem;
        height: 3rem;
        background: var(--primary-color);
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: 1.25rem;
      }

      .feature-highlight-text h3 {
        font-weight: 600;
        margin-bottom: 0.25rem;
        color: var(--text-dark);
      }

      .feature-highlight-text p {
        color: var(--text-light);
        font-size: 0.875rem;
      }

      /* How It Works Section */
      .how-it-works {
        padding: 5rem 0;
        background: var(--white);
      }

      .steps-container {
        position: relative;
      }

      .steps-line {
        display: none;
        position: absolute;
        top: 6rem;
        left: 50%;
        transform: translateX(-50%);
        width: 75%;
        height: 2px;
        background: linear-gradient(to right, var(--primary-color), var(--primary-glow));
        opacity: 0.2;
      }

      .steps-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
      }

      .step-item {
        text-align: center;
      }

      .step-icon-container {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 1.5rem;
      }

      .step-icon {
        width: 5rem;
        height: 5rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-glow));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: 2rem;
        box-shadow: var(--shadow-lg);
        margin-bottom: 1rem;
      }

      .step-number {
        position: absolute;
        top: -0.5rem;
        right: -0.5rem;
        width: 2rem;
        height: 2rem;
        background: var(--white);
        border: 2px solid var(--primary-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        color: var(--primary-color);
        font-size: 0.875rem;
        box-shadow: var(--shadow);
      }

      .step-item h3 {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--text-dark);
        margin-bottom: 1.5rem;
      }

      .step-card {
        background: var(--white);
        padding: 1.5rem;
        border-radius: 1rem;
        box-shadow: var(--shadow);
        transition: all 0.3s ease;
        text-align: left;
      }

      .step-card:hover {
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
      }

      .step-card p {
        color: var(--text-light);
        margin-bottom: 1rem;
        line-height: 1.6;
      }

      .step-card ul {
        list-style: none;
      }

      .step-card li {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
        color: var(--text-dark);
        font-size: 0.875rem;
      }

      .step-card li::before {
        content: "";
        width: 0.375rem;
        height: 0.375rem;
        background: var(--primary-color);
        border-radius: 50%;
      }

      /* Footer */
      .footer {
        background: var(--gray-900);
        color: var(--white);
        padding: 4rem 0 2rem;
      }

      .footer-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
      }

      .footer-logo-container {
        display: flex;
        align-items: center;
        gap: 0.3rem;
        margin-bottom: 1rem;
      }

      .footer-logo-k {
        font-size: 2rem;
        font-weight: 900;
        color: var(--primary-color);
        position: relative;
        display: inline-block;
      }

      .footer-logo-k::after {
        content: '🛒';
        position: absolute;
        top: -0.2rem;
        right: -0.4rem;
        font-size: 1rem;
        opacity: 0.7;
      }

      .footer-logo-text {
        font-size: 1.8rem;
        font-weight: 800;
        color: var(--primary-color);
      }

      .footer-brand p {
        color: #D1D5DB;
        margin-bottom: 1.5rem;
        line-height: 1.6;
      }

      .footer-app {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .footer-app-icon {
        width: 2.5rem;
        height: 2.5rem;
        background: rgba(220, 38, 38, 0.2);
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary-color);
        font-size: 1.25rem;
      }

      .footer-links h4 {
        font-size: 1.125rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
      }

      .footer-links ul {
        list-style: none;
      }

      .footer-links li {
        margin-bottom: 0.75rem;
      }

      .footer-links a {
        color: #D1D5DB;
        text-decoration: none;
        transition: color 0.3s ease;
      }

      .footer-links a:hover {
        color: var(--primary-color);
      }

      .footer-contact h4 {
        font-size: 1.125rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
      }

      .contact-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
        color: #D1D5DB;
      }

      .contact-item i {
        color: var(--primary-color);
        font-size: 1.25rem;
      }

      .footer-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 2rem;
        border-top: 1px solid #374151;
        color: #9CA3AF;
      }

      .footer-social {
        display: flex;
        gap: 1rem;
      }

      .footer-social a {
        width: 2.5rem;
        height: 2.5rem;
        background: rgba(220, 38, 38, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary-color);
        text-decoration: none;
        transition: all 0.3s ease;
      }

      .footer-social a:hover {
        background: var(--primary-color);
        color: var(--white);
        transform: translateY(-2px);
      }

      /* Responsive Design */
      @media (max-width: 1024px) {
        .steps-line {
          display: block;
        }

        .hero-content {
          grid-template-columns: 1fr;
          text-align: center;
          gap: 2rem;
        }

        .hero-title {
          font-size: 3rem;
        }

        .phone-mockup {
          width: 16rem;
          height: 30rem;
        }
      }

      @media (max-width: 768px) {
        .nav-menu {
          position: fixed;
          top: 100%;
          left: 0;
          width: 100%;
          background: var(--white);
          flex-direction: column;
          padding: 2rem;
          box-shadow: var(--shadow);
          transform: translateY(-100%);
          opacity: 0;
          visibility: hidden;
          transition: all 0.3s ease;
        }

        .nav-menu.active {
          transform: translateY(0);
          opacity: 1;
          visibility: visible;
        }

        .nav-toggle {
          display: flex;
        }

        .auth-dropdown {
          width: 100%;
          margin-top: 1rem;
        }

        .auth-btn {
          width: 100%;
          justify-content: center;
        }

        .auth-menu {
          position: static;
          transform: none;
          margin-top: 0.5rem;
          width: 100%;
          box-shadow: none;
          border: 1px solid var(--gray-100);
        }

        .auth-dropdown.active .auth-menu {
          transform: none;
        }

        .hero-title {
          font-size: 2.5rem;
        }

        .hero-description {
          font-size: 1.125rem;
        }

        .hero-buttons {
          flex-direction: column;
          align-items: center;
        }

        .hero-features {
          grid-template-columns: 1fr;
        }

        .section-header h2 {
          font-size: 2.5rem;
        }

        .services-grid {
          grid-template-columns: 1fr;
        }

        .steps-grid {
          grid-template-columns: 1fr;
        }

        .steps-line {
          display: none;
        }

        .footer-bottom {
          flex-direction: column;
          gap: 1rem;
          text-align: center;
        }
      }

      @media (max-width: 480px) {
        .container {
          padding: 0 0.75rem;
        }

        .hero-title {
          font-size: 2rem;
        }

        .section-header h2 {
          font-size: 2rem;
        }

        .phone-mockup {
          width: 14rem;
          height: 26rem;
          padding: 1rem;
        }

        .phone-services {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      /* Animations */
      @keyframes ripple {
        to {
          transform: scale(4);
          opacity: 0;
        }
      }

      .fade-in {
        animation: fadeIn 0.6s ease-in;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    </style>
  </head>

  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="container">
        <div class="nav-brand">
          <div class="logo-container">
            <span class="logo-k">K</span>
            <span class="logo-text">inga</span>
          </div>
        </div>
        <div class="nav-menu" id="nav-menu">
          <a href="#services" class="nav-link">Services</a>
          <a href="#comment-ca-marche" class="nav-link">Comment ça marche</a>
          <a href="#contact" class="nav-link">Contact</a>
          <div class="auth-dropdown">
            <button class="btn btn-primary auth-btn" id="auth-btn">
              <i class="fas fa-user"></i>
              Connexion / Inscription
              <i class="fas fa-chevron-down"></i>
            </button>
            <div class="auth-menu" id="auth-menu">
              <div class="auth-section">
                <h4>Utilisateur</h4>
                <a href="https://kinga.shop/user/login" class="auth-link">
                  <i class="fas fa-sign-in-alt"></i>
                  Se connecter
                </a>
                <a href="https://kinga.shop/user/signup" class="auth-link">
                  <i class="fas fa-user-plus"></i>
                  S'inscrire
                </a>
              </div>

              <div class="auth-section">
                <h4>Prestataire</h4>
                <a href="https://kinga.shop/provider/login" class="auth-link">
                  <i class="fas fa-sign-in-alt"></i>
                  Se connecter
                </a>
                <a href="https://kinga.shop/provider/signup" class="auth-link">
                  <i class="fas fa-user-plus"></i>
                  S'inscrire
                </a>
              </div>

              <div class="auth-section">
                <h4>Boutique</h4>
                <a href="https://kinga.shop/shop/login" class="auth-link">
                  <i class="fas fa-sign-in-alt"></i>
                  Se connecter
                </a>
              </div>
            </div>
          </div>
        </div>
        <div class="nav-toggle" id="nav-toggle">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-bg">
        <div class="hero-decoration hero-decoration-1"></div>
        <div class="hero-decoration hero-decoration-2"></div>
        <div class="hero-decoration hero-decoration-3"></div>
      </div>

      <div class="container">
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">
              Kinga centre<br>
              <span class="hero-subtitle">commercial</span>
            </h1>

            <p class="hero-description">
              Des services à la demande accessibles immédiatement et en temps-réel
              à partir d'une application unique à Madagascar.
            </p>

            <div class="hero-buttons">
              <button class="btn btn-secondary">
                <i class="fab fa-apple"></i>
                App Store
              </button>
              <button class="btn btn-secondary">
                <i class="fab fa-google-play"></i>
                Google Play
              </button>
            </div>

            <div class="hero-features">
              <div class="feature-item">
                <div class="feature-icon">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="feature-text">
                  <h3>Géolocalisation</h3>
                  <p>Suivi en temps réel</p>
                </div>
              </div>

              <div class="feature-item">
                <div class="feature-icon">
                  <i class="fas fa-clock"></i>
                </div>
                <div class="feature-text">
                  <h3>Livraison rapide</h3>
                  <p>Service immédiat</p>
                </div>
              </div>
            </div>
          </div>

          <div class="hero-phone">
            <div class="phone-mockup">
              <div class="phone-screen">
                <div class="phone-header">
                  <h2>Kinga</h2>
                  <p>Votre assistant de livraison</p>
                </div>

                <div class="phone-services">
                  <div class="service-icon">🚚<span>Livraison</span></div>
                  <div class="service-icon">🛒<span>Shopping</span></div>
                  <div class="service-icon">🏪<span>Commerces</span></div>
                  <div class="service-icon">🍽️<span>Restaurant</span></div>
                  <div class="service-icon">⚡<span>Express</span></div>
                  <div class="service-icon">📦<span>Colis</span></div>
                </div>

                <div class="phone-map">
                  <i class="fas fa-map-marker-alt"></i>
                </div>

                <button class="phone-btn">Nouvelle commande</button>
              </div>
            </div>

            <div class="phone-floating phone-floating-1">
              <i class="fas fa-shield-alt"></i>
            </div>
            <div class="phone-floating phone-floating-2">
              <i class="fas fa-mobile-alt"></i>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services">
      <div class="container">
        <div class="section-header">
          <h2>UNE APP <span class="text-primary">POUR TOUS LES SERVICES</span></h2>
          <p>Commander facilement des services ou des produits via l'application Kinga.
             Une plate-forme digitale innovante qui réinvente la relation entre professionnels et clients.</p>
        </div>

        <div class="services-grid">
          <div class="service-card">
            <div class="service-icon">
              <i class="fas fa-truck"></i>
            </div>
            <h3>Livraison Express</h3>
            <p>Livraison rapide et sécurisée partout à Madagascar avec suivi en temps réel.</p>
            <ul class="service-features">
              <li>Suivi GPS</li>
              <li>Livraison 24h/7j</li>
              <li>Support client</li>
            </ul>
          </div>

          <div class="service-card">
            <div class="service-icon">
              <i class="fas fa-box"></i>
            </div>
            <h3>Envoi de Colis</h3>
            <p>Envoyez vos colis facilement avec notre réseau de livreurs professionnels.</p>
            <ul class="service-features">
              <li>Tarifs compétitifs</li>
              <li>Assurance incluse</li>
              <li>Codes de sécurité</li>
            </ul>
          </div>

          <div class="service-card">
            <div class="service-icon">
              <i class="fas fa-shopping-bag"></i>
            </div>
            <h3>Shopping à la demande</h3>
            <p>Commandez dans vos magasins préférés et recevez vos achats rapidement.</p>
            <ul class="service-features">
              <li>Large réseau</li>
              <li>Paiement sécurisé</li>
              <li>Choix flexible</li>
            </ul>
          </div>
        </div>

        <div class="features-highlight">
          <div class="feature-highlight">
            <div class="feature-highlight-icon">
              <i class="fas fa-map-marker-alt"></i>
            </div>
            <div class="feature-highlight-text">
              <h3>Géolocalisation précise</h3>
              <p>Suivi en temps réel de vos commandes</p>
            </div>
          </div>

          <div class="feature-highlight">
            <div class="feature-highlight-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="feature-highlight-text">
              <h3>Livraison rapide</h3>
              <p>Service disponible 24h/24 et 7j/7</p>
            </div>
          </div>

          <div class="feature-highlight">
            <div class="feature-highlight-icon">
              <i class="fas fa-shield-alt"></i>
            </div>
            <div class="feature-highlight-text">
              <h3>Sécurisé</h3>
              <p>Codes de collecte et livraison uniques</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- How It Works Section -->
    <section id="comment-ca-marche" class="how-it-works">
      <div class="container">
        <div class="section-header">
          <h2>Comment ça <span class="text-primary">fonctionne</span></h2>
          <p>Un processus simple en 4 étapes pour bénéficier de nos services de livraison géolocalisée.</p>
        </div>

        <div class="steps-container">
          <div class="steps-line"></div>

          <div class="steps-grid">
            <div class="step-item">
              <div class="step-icon-container">
                <div class="step-icon">
                  <i class="fas fa-user-plus"></i>
                </div>
                <div class="step-number">1</div>
              </div>
              <h3>Créer un compte</h3>
              <div class="step-card">
                <p>Inscrivez-vous rapidement avec vos informations de base et activez la géolocalisation.</p>
                <ul>
                  <li>Formulaire simple</li>
                  <li>Vérification rapide</li>
                  <li>Autorisation GPS</li>
                </ul>
              </div>
            </div>

            <div class="step-item">
              <div class="step-icon-container">
                <div class="step-icon">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="step-number">2</div>
              </div>
              <h3>Choisir les adresses</h3>
              <div class="step-card">
                <p>Entrez l'adresse de collecte et de livraison avec toutes les instructions nécessaires.</p>
                <ul>
                  <li>Adresse de collecte</li>
                  <li>Adresse de livraison</li>
                  <li>Instructions spéciales</li>
                </ul>
              </div>
            </div>

            <div class="step-item">
              <div class="step-icon-container">
                <div class="step-icon">
                  <i class="fas fa-truck"></i>
                </div>
                <div class="step-number">3</div>
              </div>
              <h3>Sélectionner le service</h3>
              <div class="step-card">
                <p>Choisissez le type de véhicule adapté et votre mode de paiement préféré.</p>
                <ul>
                  <li>Type de véhicule</li>
                  <li>Mode de paiement</li>
                  <li>Estimation tarifaire</li>
                </ul>
              </div>
            </div>

            <div class="step-item">
              <div class="step-icon-container">
                <div class="step-icon">
                  <i class="fas fa-check-circle"></i>
                </div>
                <div class="step-number">4</div>
              </div>
              <h3>Suivi en temps réel</h3>
              <div class="step-card">
                <p>Recevez vos codes de sécurité et suivez votre commande en temps réel.</p>
                <ul>
                  <li>Code de collecte</li>
                  <li>Code de livraison</li>
                  <li>Suivi GPS</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer id="contact" class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-brand">
            <div class="footer-logo-container">
              <span class="footer-logo-k">K</span>
              <span class="footer-logo-text">inga</span>
            </div>
            <p>La plateforme de livraison à la demande qui révolutionne les services à Madagascar. Rapide, sécurisé, géolocalisé.</p>
            <div class="footer-app">
              <div class="footer-app-icon">
                <i class="fas fa-mobile-alt"></i>
              </div>
              <div>
                <p><strong>Téléchargez l'app</strong></p>
                <p>Disponible sur iOS et Android</p>
              </div>
            </div>
          </div>

          <div class="footer-links">
            <h4>Services</h4>
            <ul>
              <li><a href="#services">Livraison Express</a></li>
              <li><a href="#services">Envoi de Colis</a></li>
              <li><a href="#services">Shopping</a></li>
              <li><a href="#services">Restaurants</a></li>
            </ul>
          </div>

          <div class="footer-links">
            <h4>Support</h4>
            <ul>
              <li><a href="#comment-ca-marche">Comment ça marche</a></li>
              <li><a href="#">Centre d'aide</a></li>
              <li><a href="#">Conditions d'utilisation</a></li>
              <li><a href="#">Politique de confidentialité</a></li>
            </ul>
          </div>

          <div class="footer-contact">
            <h4>Contact</h4>
            <div class="contact-item">
              <i class="fas fa-map-marker-alt"></i>
              <span>Antananarivo, Madagascar</span>
            </div>
            <div class="contact-item">
              <i class="fas fa-phone"></i>
              <span>+261 XX XX XXX XX</span>
            </div>
            <div class="contact-item">
              <i class="fas fa-envelope"></i>
              <span><EMAIL></span>
            </div>
          </div>
        </div>

        <div class="footer-bottom">
          <p>&copy; 2024 Kinga Madagascar. Tous droits réservés.</p>
          <div class="footer-social">
            <a href="#"><i class="fab fa-facebook"></i></a>
            <a href="#"><i class="fab fa-twitter"></i></a>
            <a href="#"><i class="fab fa-instagram"></i></a>
            <a href="#"><i class="fab fa-linkedin"></i></a>
          </div>
        </div>
      </div>
    </footer>

    <script>
      // Mobile Navigation Toggle
      document.addEventListener('DOMContentLoaded', function() {
          const navToggle = document.getElementById('nav-toggle');
          const navMenu = document.getElementById('nav-menu');

          if (navToggle && navMenu) {
              navToggle.addEventListener('click', function() {
                  navMenu.classList.toggle('active');

                  // Animate hamburger menu
                  const spans = navToggle.querySelectorAll('span');
                  spans.forEach((span, index) => {
                      if (navMenu.classList.contains('active')) {
                          if (index === 0) span.style.transform = 'rotate(45deg) translate(5px, 5px)';
                          if (index === 1) span.style.opacity = '0';
                          if (index === 2) span.style.transform = 'rotate(-45deg) translate(7px, -6px)';
                      } else {
                          span.style.transform = 'none';
                          span.style.opacity = '1';
                      }
                  });
              });
          }

          // Auth Dropdown Toggle
          const authBtn = document.getElementById('auth-btn');
          const authDropdown = document.querySelector('.auth-dropdown');

          if (authBtn && authDropdown) {
              authBtn.addEventListener('click', function(e) {
                  e.preventDefault();
                  e.stopPropagation();
                  authDropdown.classList.toggle('active');
              });

              // Close dropdown when clicking outside
              document.addEventListener('click', function(e) {
                  if (!authDropdown.contains(e.target)) {
                      authDropdown.classList.remove('active');
                  }
              });

              // Close dropdown when clicking on auth links
              const authLinks = document.querySelectorAll('.auth-link');
              authLinks.forEach(link => {
                  link.addEventListener('click', function() {
                      authDropdown.classList.remove('active');
                  });
              });
          }

          // Close mobile menu when clicking on a link
          const navLinks = document.querySelectorAll('.nav-link');
          navLinks.forEach(link => {
              link.addEventListener('click', function() {
                  if (navMenu.classList.contains('active')) {
                      navMenu.classList.remove('active');
                      const spans = navToggle.querySelectorAll('span');
                      spans.forEach(span => {
                          span.style.transform = 'none';
                          span.style.opacity = '1';
                      });
                  }
              });
          });
      });

      // Smooth Scrolling for Navigation Links
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
          anchor.addEventListener('click', function (e) {
              e.preventDefault();
              const target = document.querySelector(this.getAttribute('href'));
              if (target) {
                  const offsetTop = target.offsetTop - 80; // Account for fixed navbar
                  window.scrollTo({
                      top: offsetTop,
                      behavior: 'smooth'
                  });
              }
          });
      });

      // Navbar Background on Scroll
      window.addEventListener('scroll', function() {
          const navbar = document.querySelector('.navbar');
          if (window.scrollY > 50) {
              navbar.style.background = 'rgba(255, 255, 255, 0.98)';
              navbar.style.backdropFilter = 'blur(20px)';
          } else {
              navbar.style.background = 'rgba(255, 255, 255, 0.95)';
              navbar.style.backdropFilter = 'blur(10px)';
          }
      });

      // Intersection Observer for Animations
      const observerOptions = {
          threshold: 0.1,
          rootMargin: '0px 0px -50px 0px'
      };

      const observer = new IntersectionObserver(function(entries) {
          entries.forEach(entry => {
              if (entry.isIntersecting) {
                  entry.target.style.opacity = '1';
                  entry.target.style.transform = 'translateY(0)';
              }
          });
      }, observerOptions);

      // Observe elements for animation
      document.addEventListener('DOMContentLoaded', function() {
          const animatedElements = document.querySelectorAll('.service-card, .step-item, .feature-highlight');

          animatedElements.forEach(el => {
              el.style.opacity = '0';
              el.style.transform = 'translateY(30px)';
              el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
              observer.observe(el);
          });
      });

      // Phone Mockup Interaction
      document.addEventListener('DOMContentLoaded', function() {
          const phoneBtn = document.querySelector('.phone-btn');
          const serviceIcons = document.querySelectorAll('.service-icon');

          if (phoneBtn) {
              phoneBtn.addEventListener('click', function() {
                  // Add a ripple effect
                  const ripple = document.createElement('span');
                  ripple.style.position = 'absolute';
                  ripple.style.borderRadius = '50%';
                  ripple.style.background = 'rgba(255, 255, 255, 0.6)';
                  ripple.style.transform = 'scale(0)';
                  ripple.style.animation = 'ripple 0.6s linear';
                  ripple.style.left = '50%';
                  ripple.style.top = '50%';
                  ripple.style.width = '20px';
                  ripple.style.height = '20px';
                  ripple.style.marginLeft = '-10px';
                  ripple.style.marginTop = '-10px';

                  this.style.position = 'relative';
                  this.appendChild(ripple);

                  setTimeout(() => {
                      ripple.remove();
                  }, 600);
              });
          }

          // Add hover effects to service icons
          serviceIcons.forEach(icon => {
              icon.addEventListener('mouseenter', function() {
                  this.style.transform = 'scale(1.05)';
                  this.style.transition = 'transform 0.2s ease';
              });

              icon.addEventListener('mouseleave', function() {
                  this.style.transform = 'scale(1)';
              });
          });
      });

      // Initialize all functionality when DOM is loaded
      document.addEventListener('DOMContentLoaded', function() {
          console.log('Kinga Madagascar Delivery - Site loaded successfully!');

          // Add loading animation to images
          const images = document.querySelectorAll('img');
          images.forEach(img => {
              img.addEventListener('load', function() {
                  this.style.opacity = '1';
              });
              img.style.opacity = '0';
              img.style.transition = 'opacity 0.3s ease';
          });
      });
    </script>
  </body>
</html>
